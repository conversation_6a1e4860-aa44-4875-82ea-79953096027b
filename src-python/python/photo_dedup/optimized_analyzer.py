"""
优化的照片分析器：分层检测 + 批量处理 + 数据库缓存 + 并行计算
"""

import json
import logging
import os
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, List, Tuple

import imagehash
import psutil
from PIL import Image

from .database import PerformanceMetrics, PhotoHashCache, initialize_database
from .models import PhotoInfo
from .models import SimilarityGroup as SimilarityGroupModel

logger = logging.getLogger(__name__)


@dataclass
class BatchProcessingConfig:
    """批量处理配置"""

    batch_size: int = 50
    max_workers: int = 4
    ahash_threshold: float = 0.50  # AHash第一轮筛选阈值 (降低以找到更多候选)
    phash_threshold: float = 0.50  # PHash第二轮验证阈值 (降低以找到更多相似组)
    enable_cache: bool = True
    cache_db_path: str = None  # 将在初始化时设置为临时目录

    def __post_init__(self):
        """初始化后处理，设置默认的缓存数据库路径"""
        if self.cache_db_path is None:
            import tempfile
            import os
            temp_dir = tempfile.gettempdir()
            self.cache_db_path = os.path.join(temp_dir, "photo_manager.db")


class OptimizedPhotoAnalyzer:
    """优化的照片分析器"""

    def __init__(self, config: BatchProcessingConfig = None):
        self.config = config or BatchProcessingConfig()
        self.session_factory = initialize_database(self.config.cache_db_path)
        self.performance_stats = {"cache_hits": 0, "cache_misses": 0, "total_photos_processed": 0, "ahash_calculations": 0, "phash_calculations": 0}

    def analyze_photos_optimized(self, photos: List[PhotoInfo]) -> Dict[str, Any]:
        """
        优化的照片分析：分层检测 + 批量处理 + 并行计算

        Args:
            photos: 要分析的照片列表

        Returns:
            分析结果字典
        """
        start_time = time.time()
        logger.info(f"🚀 开始优化分析 {len(photos)} 张照片")

        # 构建UUID到PhotoInfo的映射
        all_photos = {photo.uuid: photo for photo in photos}

        # 第一阶段：批量计算AHash（并行）
        logger.info("📊 第一阶段：AHash快速筛选")
        ahash_results = self._batch_calculate_ahash(photos)

        # 第二阶段：AHash相似度筛选
        logger.info("🔍 第二阶段：AHash相似度筛选")
        ahash_candidates = self._filter_by_ahash(ahash_results)

        # 第三阶段：PHash精确验证（只对候选项）
        logger.info("🎯 第三阶段：PHash精确验证")
        similarity_groups = self._verify_with_phash(ahash_candidates, all_photos)

        # 记录性能指标
        total_time = time.time() - start_time
        self._record_performance_metrics(len(photos), total_time)

        logger.info(f"✅ 优化分析完成，耗时 {total_time:.2f}s")
        logger.info(f"📈 性能统计: 缓存命中 {self.performance_stats['cache_hits']}, 缓存未命中 {self.performance_stats['cache_misses']}")

        # 处理所有照片的hash值，确保前端能够正确显示
        self._process_photos_for_frontend(photos)

        # 处理相似组中的照片hash值
        for group in similarity_groups:
            self._process_photos_for_frontend(group.photos)

        return {"similarity_groups": [group.to_dict() for group in similarity_groups], "performance_stats": self.performance_stats, "total_time_seconds": total_time}

    def _process_photos_for_frontend(self, photos: List[PhotoInfo]):
        """处理照片数据，确保前端能够正确显示hash值"""
        for photo in photos:
            if photo.hash_values:
                # 将hash_values字典中的值设置为直接属性，以便前端显示
                for hash_type, hash_value in photo.hash_values.items():
                    setattr(photo, hash_type, hash_value)

                logger.info(f"✅ 为照片 {photo.filename} 设置hash属性: {list(photo.hash_values.keys())}")
            else:
                logger.warning(f"⚠️ 照片 {photo.filename} 没有hash_values")

    def _batch_calculate_ahash(self, photos: List[PhotoInfo]) -> Dict[str, Dict]:
        """批量并行计算AHash值"""
        results = {}

        # 分批处理
        for i in range(0, len(photos), self.config.batch_size):
            batch = photos[i : i + self.config.batch_size]
            logger.info(f"处理批次 {i // self.config.batch_size + 1}: {len(batch)} 张照片")

            # 检查缓存
            cached_results, uncached_photos = self._check_hash_cache(batch, ["ahash"])
            results.update(cached_results)

            if uncached_photos:
                # 并行计算未缓存的照片
                batch_results = self._parallel_calculate_hashes(uncached_photos, ["ahash"])
                results.update(batch_results)

                # 批量保存到缓存
                self._batch_save_to_cache(batch_results)

        return results

    def _parallel_calculate_hashes(self, photos: List[PhotoInfo], algorithms: List[str]) -> Dict[str, Dict]:
        """并行计算hash值"""
        results = {}

        with ThreadPoolExecutor(max_workers=self.config.max_workers) as executor:
            # 提交任务
            future_to_photo = {executor.submit(self._calculate_single_photo_hashes, photo, algorithms): photo for photo in photos}

            # 收集结果
            for future in as_completed(future_to_photo):
                photo = future_to_photo[future]
                try:
                    hash_result = future.result()
                    results[photo.uuid] = hash_result
                except Exception as e:
                    logger.error(f"计算 {photo.filename} 的hash失败: {e}")

        return results

    def _calculate_single_photo_hashes(self, photo: PhotoInfo, algorithms: List[str]) -> Dict:
        """计算单张照片的hash值"""
        result = {"photo": photo, "hashes": {}, "timing": {}, "total_time": 0}

        try:
            image_path = photo.thumbnail_path or photo.original_path
            if not os.path.exists(image_path):
                return result

            total_start = time.time()

            with Image.open(image_path) as img:
                if img.mode != "RGB":
                    img = img.convert("RGB")

                # 按算法计算
                for algorithm in algorithms:
                    start_time = time.time()

                    if algorithm == "ahash":
                        hash_value = str(imagehash.average_hash(img))
                        self.performance_stats["ahash_calculations"] += 1
                    elif algorithm == "dhash":
                        hash_value = str(imagehash.dhash(img))
                    elif algorithm == "phash":
                        hash_value = str(imagehash.phash(img))
                        self.performance_stats["phash_calculations"] += 1
                    elif algorithm == "whash":
                        hash_value = str(imagehash.whash(img))
                    else:
                        continue

                    execution_time = (time.time() - start_time) * 1000
                    result["hashes"][algorithm] = hash_value
                    result["timing"][f"{algorithm}_time_ms"] = execution_time

            result["total_time"] = (time.time() - total_start) * 1000

        except Exception as e:
            logger.error(f"处理图片 {photo.filename} 失败: {e}")

        return result

    def _check_hash_cache(self, photos: List[PhotoInfo], algorithms: List[str]) -> Tuple[Dict, List[PhotoInfo]]:
        """检查hash缓存"""
        if not self.config.enable_cache:
            return {}, photos

        cached_results = {}
        uncached_photos = []

        session = self.session_factory()
        try:
            for photo in photos:
                # 检查文件修改时间
                try:
                    file_mtime = os.path.getmtime(photo.thumbnail_path or photo.original_path)
                except OSError:
                    uncached_photos.append(photo)
                    continue

                # 查询缓存
                cache_entry = session.query(PhotoHashCache).filter_by(photo_uuid=photo.uuid).first()

                if cache_entry and cache_entry.file_mtime == file_mtime and all(getattr(cache_entry, alg) for alg in algorithms):
                    # 缓存命中
                    hash_values = {alg: getattr(cache_entry, alg) for alg in algorithms}
                    cached_results[photo.uuid] = {"photo": photo, "hashes": hash_values, "timing": {f"{alg}_time_ms": getattr(cache_entry, f"{alg}_time_ms", 0) for alg in algorithms}, "total_time": cache_entry.total_time_ms or 0, "from_cache": True}

                    # 更新PhotoInfo对象的hash_values字典
                    if photo.hash_values is None:
                        photo.hash_values = {}
                    photo.hash_values.update(hash_values)

                    # 更新缓存命中次数
                    cache_entry.cache_hit += 1
                    self.performance_stats["cache_hits"] += 1
                else:
                    uncached_photos.append(photo)
                    self.performance_stats["cache_misses"] += 1

        finally:
            session.commit()
            session.close()

        return cached_results, uncached_photos

    def _batch_save_to_cache(self, results: Dict[str, Dict]):
        """批量保存hash结果到缓存"""
        if not self.config.enable_cache:
            return

        session = self.session_factory()
        try:
            for uuid, result in results.items():
                if "from_cache" in result:
                    continue  # 跳过来自缓存的结果

                photo = result["photo"]

                try:
                    file_mtime = os.path.getmtime(photo.thumbnail_path or photo.original_path)
                except OSError:
                    continue

                # 更新或创建缓存条目
                cache_entry = session.query(PhotoHashCache).filter_by(photo_uuid=photo.uuid).first()

                if not cache_entry:
                    cache_entry = PhotoHashCache(photo_uuid=photo.uuid, filename=photo.filename, file_path=photo.thumbnail_path or photo.original_path, file_size=photo.file_size, file_mtime=file_mtime)
                    session.add(cache_entry)

                # 更新hash值和性能数据
                for alg, hash_value in result["hashes"].items():
                    setattr(cache_entry, alg, hash_value)
                    setattr(cache_entry, f"{alg}_time_ms", result["timing"].get(f"{alg}_time_ms", 0))

                cache_entry.total_time_ms = result["total_time"]
                cache_entry.updated_at = datetime.utcnow()

                # 同时更新PhotoInfo对象的hash_values字典
                if photo.hash_values is None:
                    photo.hash_values = {}
                photo.hash_values.update(result["hashes"])

        finally:
            session.commit()
            session.close()

    def _filter_by_ahash(self, ahash_results: Dict[str, Dict]) -> List[Tuple[str, str]]:
        """使用AHash进行第一轮筛选"""
        candidates = []

        # 构建AHash到照片UUID的映射
        ahash_to_photos = {}
        for uuid, result in ahash_results.items():
            ahash = result["hashes"].get("ahash")
            if ahash:
                if ahash not in ahash_to_photos:
                    ahash_to_photos[ahash] = []
                ahash_to_photos[ahash].append(uuid)

        # 找出相似的AHash组
        processed_hashes = set()
        for ahash, photo_uuids in ahash_to_photos.items():
            if ahash in processed_hashes:
                continue

            similar_group = [ahash]

            # 查找相似的AHash值
            for other_ahash in ahash_to_photos:
                if other_ahash != ahash and other_ahash not in processed_hashes:
                    # 计算汉明距离
                    try:
                        h1 = imagehash.hex_to_hash(ahash)
                        h2 = imagehash.hex_to_hash(other_ahash)
                        similarity = 1.0 - (h1 - h2) / 64.0

                        if similarity >= self.config.ahash_threshold:
                            similar_group.append(other_ahash)
                            processed_hashes.add(other_ahash)
                    except:
                        continue

            # 如果找到相似组，添加到候选列表
            if len(similar_group) > 1:
                for ahash in similar_group:
                    for uuid in ahash_to_photos[ahash]:
                        candidates.extend([(uuid, other_uuid) for other_uuid in ahash_to_photos[ahash] if other_uuid != uuid])

            processed_hashes.add(ahash)

        logger.info(f"AHash筛选找到 {len(candidates)} 个候选对")

        # 调试信息：显示一些候选对的详情
        if candidates:
            logger.info(f"🔍 前5个候选对示例: {candidates[:5]}")
        else:
            logger.warning("⚠️ 没有找到任何候选对，检查AHash分布:")
            # 显示AHash分布情况
            hash_counts = {}
            for ahash, photo_uuids in ahash_to_photos.items():
                count = len(photo_uuids)
                if count > 1:
                    hash_counts[ahash] = count

            if hash_counts:
                logger.info(f"🔍 重复AHash值: {dict(list(hash_counts.items())[:5])}")
                # 检查第一个重复hash的相似度
                first_hash = list(hash_counts.keys())[0]
                for other_hash in list(hash_counts.keys())[1:2]:  # 只检查一个
                    try:
                        h1 = imagehash.hex_to_hash(first_hash)
                        h2 = imagehash.hex_to_hash(other_hash)
                        similarity = 1.0 - (h1 - h2) / 64.0
                        logger.info(f"🔍 相似度测试: {first_hash} vs {other_hash} = {similarity:.3f} (阈值: {self.config.ahash_threshold})")
                    except Exception as e:
                        logger.warning(f"相似度计算失败: {e}")
            else:
                logger.warning("没有找到重复的AHash值")

        return candidates

    def _verify_with_phash(self, candidates: List[Tuple[str, str]], all_photos: Dict[str, PhotoInfo]) -> List[SimilarityGroupModel]:
        """使用PHash进行第二轮精确验证"""
        if not candidates:
            return []

        # 获取需要PHash的照片UUID
        unique_uuids = set()
        for uuid1, uuid2 in candidates:
            unique_uuids.add(uuid1)
            unique_uuids.add(uuid2)

        logger.info(f"需要计算PHash的照片: {len(unique_uuids)} 张")

        # 获取对应的PhotoInfo对象
        photos_for_phash = []
        for uuid in unique_uuids:
            if uuid in all_photos:
                photos_for_phash.append(all_photos[uuid])

        # 批量计算PHash（检查缓存 + 并行计算）
        cached_results, uncached_photos = self._check_hash_cache(photos_for_phash, ["phash"])

        if uncached_photos:
            phash_results = self._parallel_calculate_hashes(uncached_photos, ["phash"])
            cached_results.update(phash_results)
            self._batch_save_to_cache(phash_results)

        # 使用PHash进行精确相似度验证
        verified_pairs = []
        for uuid1, uuid2 in candidates:
            if uuid1 in cached_results and uuid2 in cached_results:
                result1 = cached_results[uuid1]
                result2 = cached_results[uuid2]

                phash1 = result1["hashes"].get("phash")
                phash2 = result2["hashes"].get("phash")

                if phash1 and phash2:
                    try:
                        h1 = imagehash.hex_to_hash(phash1)
                        h2 = imagehash.hex_to_hash(phash2)
                        similarity = 1.0 - (h1 - h2) / 64.0

                        if similarity >= self.config.phash_threshold:
                            verified_pairs.append((uuid1, uuid2, similarity))
                    except Exception as e:
                        logger.warning(f"PHash比较失败 {uuid1} vs {uuid2}: {e}")

        logger.info(f"PHash验证通过 {len(verified_pairs)} 个相似对")

        # 构建相似度组
        similarity_groups = self._build_similarity_groups(verified_pairs, all_photos)
        return similarity_groups

    def _build_similarity_groups(self, verified_pairs: List[Tuple[str, str, float]], all_photos: Dict[str, PhotoInfo]) -> List[SimilarityGroupModel]:
        """构建相似度组"""
        if not verified_pairs:
            return []

        # 使用并查集算法构建连通组
        parent = {}

        def find(x):
            if x not in parent:
                parent[x] = x
            if parent[x] != x:
                parent[x] = find(parent[x])
            return parent[x]

        def union(x, y):
            px, py = find(x), find(y)
            if px != py:
                parent[px] = py

        # 构建连通组
        for uuid1, uuid2, similarity in verified_pairs:
            union(uuid1, uuid2)

        # 分组
        groups = {}
        for uuid1, uuid2, similarity in verified_pairs:
            root = find(uuid1)
            if root not in groups:
                groups[root] = {"photos": set(), "similarities": [], "max_similarity": 0.0}
            groups[root]["photos"].add(uuid1)
            groups[root]["photos"].add(uuid2)
            groups[root]["similarities"].append(similarity)
            groups[root]["max_similarity"] = max(groups[root]["max_similarity"], similarity)

        # 转换为SimilarityGroupModel
        similarity_groups = []
        for i, (root, group_data) in enumerate(groups.items()):
            if len(group_data["photos"]) < 2:
                continue

            group_photos = []
            time_range_start = None
            time_range_end = None

            for uuid in group_data["photos"]:
                if uuid in all_photos:
                    photo = all_photos[uuid]
                    group_photos.append(photo)

                    # photo.date_taken 已经是 datetime 对象，不需要转换
                    photo_time = photo.date_taken
                    if time_range_start is None or photo_time < time_range_start:
                        time_range_start = photo_time
                    if time_range_end is None or photo_time > time_range_end:
                        time_range_end = photo_time

            if len(group_photos) >= 2 and time_range_start and time_range_end:
                duration = (time_range_end - time_range_start).total_seconds()

                similarity_group = SimilarityGroupModel(
                    group_id=f"optimized_group_{i}",
                    photos=group_photos,
                    similarity_score=group_data["max_similarity"],
                    time_range=(time_range_start, time_range_end)
                )
                similarity_groups.append(similarity_group)

        logger.info(f"构建了 {len(similarity_groups)} 个相似度组")
        return similarity_groups

    def _record_performance_metrics(self, photo_count: int, total_time: float):
        """记录性能指标到数据库"""
        session = self.session_factory()
        try:
            metrics = PerformanceMetrics(
                operation_type="optimized_batch_analysis",
                photo_count=photo_count,
                total_time_ms=total_time * 1000,
                avg_time_per_photo_ms=(total_time * 1000) / photo_count if photo_count > 0 else 0,
                cpu_cores_used=self.config.max_workers,
                memory_peak_mb=psutil.Process().memory_info().rss / 1024 / 1024,
                cache_hits=self.performance_stats["cache_hits"],
                cache_misses=self.performance_stats["cache_misses"],
                cache_hit_rate=self.performance_stats["cache_hits"] / (self.performance_stats["cache_hits"] + self.performance_stats["cache_misses"]) if (self.performance_stats["cache_hits"] + self.performance_stats["cache_misses"]) > 0 else 0,
            )
            session.add(metrics)
            session.commit()
        finally:
            session.close()


def analyze_photos_optimized(photos_json: str = None, config_dict: Dict = None) -> str:
    """
    优化的照片分析入口函数（用于Rust调用）

    Args:
        photos_json: 照片列表的JSON字符串
        config_dict: 配置字典

    Returns:
        分析结果的JSON字符串
    """
    try:
        # 解析照片数据
        if photos_json:
            photos_data = json.loads(photos_json)
            photos = []
            for photo_data in photos_data:
                # 过滤掉PhotoInfo构造函数不接受的字段
                filtered_data = {k: v for k, v in photo_data.items() if k not in ["latitude", "longitude"]}

                # 如果有latitude和longitude，转换为location字段
                if "latitude" in photo_data and "longitude" in photo_data:
                    if photo_data["latitude"] is not None and photo_data["longitude"] is not None:
                        filtered_data["location"] = {"latitude": photo_data["latitude"], "longitude": photo_data["longitude"]}

                # 转换date_taken从timestamp到datetime，确保有时区信息
                if "date_taken" in filtered_data:
                    from datetime import timezone
                    filtered_data["date_taken"] = datetime.fromtimestamp(filtered_data["date_taken"], tz=timezone.utc)

                photos.append(PhotoInfo(**filtered_data))
        else:
            photos = []

        # 解析配置
        config = BatchProcessingConfig()
        if config_dict:
            if "batch_size" in config_dict:
                config.batch_size = config_dict["batch_size"]
            if "max_workers" in config_dict:
                config.max_workers = config_dict["max_workers"]
            if "ahash_threshold" in config_dict:
                config.ahash_threshold = config_dict["ahash_threshold"]
            if "phash_threshold" in config_dict:
                config.phash_threshold = config_dict["phash_threshold"]
            if "enable_cache" in config_dict:
                config.enable_cache = config_dict["enable_cache"]
            if "cache_db_path" in config_dict:
                config.cache_db_path = config_dict["cache_db_path"]

        # 执行优化分析
        analyzer = OptimizedPhotoAnalyzer(config)
        result = analyzer.analyze_photos_optimized(photos)

        # 返回JSON字符串
        return json.dumps(result, ensure_ascii=False, default=str)

    except Exception as e:
        logger.error(f"优化分析失败: {e}")
        return json.dumps({"error": str(e), "similarity_groups": [], "performance_stats": {}, "total_time_seconds": 0})


def analyze_photos_optimized_direct(photos: List[PhotoInfo], config: BatchProcessingConfig = None) -> Dict[str, Any]:
    """
    优化的照片分析直接调用函数

    Args:
        photos: 要分析的照片列表
        config: 批量处理配置

    Returns:
        分析结果字典
    """
    analyzer = OptimizedPhotoAnalyzer(config)
    return analyzer.analyze_photos_optimized(photos)
