use pyo3::prelude::*;
use pyo3::types::PyDict;
use serde_json;
use std::sync::Arc;
use tokio::sync::Mutex;

use crate::models::{LibraryInfo, PhotoError, PhotoInfo, SimilarityGroup};

/// Python bridge for photo processing operations
pub struct PythonBridge {
    _gil: Arc<Mutex<()>>,
}

impl PythonBridge {
    /// Process entire photo library: collect photos, generate thumbnails, detect similarity groups
    pub async fn process_library(
        &self,
        library_path: &str,
        output_dir: &str,
        db_path: &str,
        days_back: i32,
        max_photos: i32,
        thumbnail_size: &str,
        thumbnail_quality: i32,
        time_threshold_seconds: i32,
        similarity_threshold: f32,
    ) -> Result<serde_json::Value, PhotoError> {
        let library_path = library_path.to_string();
        let output_dir = output_dir.to_string();
        let db_path = db_path.to_string();
        let thumbnail_size = thumbnail_size.to_string();

        let result = tokio::task::spawn_blocking(move || {
            Python::with_gil(|py| {
                let sys = py.import("sys")?;

                // 获取当前工作目录并记录日志
                let current_dir = std::env::current_dir()
                    .map_err(|e| PhotoError::FileSystemError(format!("无法获取当前目录: {}", e)))?;
                log::info!("🔍 process_library - 当前工作目录: {:?}", current_dir);

                // 尝试多个可能的Python路径
                let possible_paths = vec![
                    current_dir.join("../src-python/python"),
                    current_dir.join("src-python/python"),
                    current_dir.join("photo-thumbnail-manager/src-python/python"),
                    current_dir
                        .parent()
                        .unwrap_or(&current_dir)
                        .join("photo-thumbnail-manager/src-python/python"),
                ];

                let mut python_path_str = None;
                for path in possible_paths {
                    log::info!("🔍 process_library - 尝试Python路径: {:?}", path);
                    if path.exists() {
                        match path.canonicalize() {
                            Ok(canonical_path) => {
                                python_path_str =
                                    Some(canonical_path.to_str().unwrap().to_string());
                                log::info!(
                                    "✅ process_library - 找到有效的Python路径: {:?}",
                                    canonical_path
                                );
                                break;
                            }
                            Err(e) => {
                                log::warn!(
                                    "⚠️ process_library - 路径规范化失败: {:?}, 错误: {}",
                                    path,
                                    e
                                );
                            }
                        }
                    } else {
                        log::warn!("⚠️ process_library - 路径不存在: {:?}", path);
                    }
                }

                let python_path_str = python_path_str.ok_or_else(|| {
                    PhotoError::FileSystemError("找不到有效的Python模块路径".to_string())
                })?;

                let sys_path = sys.getattr("path")?;
                sys_path.call_method1("insert", (0, &python_path_str))?;
                log::info!("🐍 process_library - 已添加Python路径: {}", python_path_str);

                let integrator_module = PyModule::import(py, "photo_dedup.integrator")?;
                let process_func = integrator_module.getattr("process_photo_library")?;

                // Prepare keyword arguments
                let kwargs = PyDict::new(py);
                kwargs.set_item("library_path", &library_path)?;
                kwargs.set_item("output_dir", &output_dir)?;
                kwargs.set_item("db_path", &db_path)?;
                kwargs.set_item("days_back", days_back)?;
                kwargs.set_item("max_photos", max_photos)?;
                kwargs.set_item("thumbnail_size", &thumbnail_size)?;
                kwargs.set_item("thumbnail_quality", thumbnail_quality)?;
                kwargs.set_item("time_threshold_seconds", time_threshold_seconds)?;
                kwargs.set_item("similarity_threshold", similarity_threshold)?;

                let result = process_func.call((), Some(&kwargs))?;
                let json_str: String = result.extract()?;
                log::info!("✅ 收到处理结果JSON长度: {} 字符", json_str.len());

                // Parse JSON result
                let parsed_result: serde_json::Value =
                    serde_json::from_str(&json_str).map_err(|e| {
                        log::error!("❌ 处理结果JSON解析失败: {}", e);
                        PhotoError::PythonError(format!("处理结果JSON解析错误: {}", e))
                    })?;

                Ok(parsed_result)
            })
        })
        .await
        .map_err(|e| PhotoError::PythonError(e.to_string()))?;

        result
    }

    pub fn new() -> Result<Self, PhotoError> {
        log::debug!("🔧 初始化 Python Bridge...");

        // Ensure Python is initialized
        Python::with_gil(|py| {
            log::debug!("🐍 Python GIL 获取成功");

            // 检查 Python 版本
            let version = py.version_info();
            log::debug!(
                "🐍 Python 版本: {}.{}.{}",
                version.major,
                version.minor,
                version.patch
            );

            // Python will be initialized on first use
            log::debug!("✅ Python Bridge 初始化完成");
            Ok(Self {
                _gil: Arc::new(Mutex::new(())),
            })
        })
    }

    /// 静态方法：为任务设置Python路径
    fn setup_python_paths_for_task(py: Python) -> Result<(), PhotoError> {
        let sys = py.import("sys")?;

        // 获取当前工作目录
        let current_dir = std::env::current_dir()
            .map_err(|e| PhotoError::FileSystemError(format!("无法获取当前目录: {}", e)))?;

        // 尝试多个可能的Python路径
        let possible_paths = vec![
            current_dir.join("../src-python/python"),
            current_dir.join("src-python/python"),
            current_dir.join("photo-thumbnail-manager/src-python/python"),
            current_dir
                .parent()
                .unwrap_or(&current_dir)
                .join("photo-thumbnail-manager/src-python/python"),
        ];

        let mut python_path_str = None;
        for path in possible_paths {
            if path.exists() {
                match path.canonicalize() {
                    Ok(canonical_path) => {
                        python_path_str = Some(canonical_path.to_str().unwrap().to_string());
                        log::info!("✅ 找到有效的Python路径: {:?}", canonical_path);
                        break;
                    }
                    Err(e) => {
                        log::warn!("⚠️ 路径规范化失败: {:?}, 错误: {}", path, e);
                    }
                }
            }
        }

        let python_path_str = python_path_str
            .ok_or_else(|| PhotoError::FileSystemError("找不到有效的Python模块路径".to_string()))?;

        let sys_path = sys.getattr("path")?;
        sys_path.call_method1("insert", (0, &python_path_str))?;
        log::info!("🐍 已添加Python路径: {}", python_path_str);

        // 动态获取当前Poetry环境的site-packages路径
        if let Ok(output) = std::process::Command::new("poetry")
            .args(&["env", "info", "--path"])
            .current_dir("../src-python")
            .output()
        {
            if output.status.success() {
                let env_path = String::from_utf8_lossy(&output.stdout).trim().to_string();
                log::info!("🔍 Poetry环境路径: {}", env_path);

                // 尝试常见的Python版本路径
                let python_versions = ["3.12", "3.13", "3.11", "3.10"];
                for version in &python_versions {
                    let site_packages_path =
                        format!("{}/lib/python{}/site-packages", env_path, version);
                    if std::path::Path::new(&site_packages_path).exists() {
                        sys_path.call_method1("insert", (0, &site_packages_path))?;
                        log::info!("🐍 已添加Poetry site-packages路径: {}", site_packages_path);
                        break;
                    }
                }
            }
        }

        Ok(())
    }

    /// Get basic information about a photo library or directory
    pub async fn get_library_info(&self, path: &str) -> Result<LibraryInfo, PhotoError> {
        log::debug!("🔍 get_library_info 开始，路径: {}", path);
        let path = path.to_string();

        // Use spawn_blocking for Python operations to avoid blocking async runtime
        let result = tokio::task::spawn_blocking(move || {
            log::debug!("🐍 进入 Python GIL 环境");
            Python::with_gil(|py| {
                log::debug!("📦 导入 sys 模块");
                let sys = py.import("sys")?;

                // 获取当前工作目录并记录日志
                let current_dir = std::env::current_dir()
                    .map_err(|e| PhotoError::FileSystemError(format!("无法获取当前目录: {}", e)))?;
                log::info!("🔍 get_library_info - 当前工作目录: {:?}", current_dir);

                // 尝试多个可能的Python路径
                let possible_paths = vec![
                    current_dir.join("../src-python/python"),
                    current_dir.join("src-python/python"),
                    current_dir.join("photo-thumbnail-manager/src-python/python"),
                    current_dir
                        .parent()
                        .unwrap_or(&current_dir)
                        .join("photo-thumbnail-manager/src-python/python"),
                ];

                let mut python_path_str = None;
                for path in possible_paths {
                    log::info!("🔍 尝试Python路径: {:?}", path);
                    if path.exists() {
                        match path.canonicalize() {
                            Ok(canonical_path) => {
                                python_path_str =
                                    Some(canonical_path.to_str().unwrap().to_string());
                                log::info!("✅ 找到有效的Python路径: {:?}", canonical_path);
                                break;
                            }
                            Err(e) => {
                                log::warn!("⚠️ 路径规范化失败: {:?}, 错误: {}", path, e);
                            }
                        }
                    } else {
                        log::warn!("⚠️ 路径不存在: {:?}", path);
                    }
                }

                let python_path_str = python_path_str.ok_or_else(|| {
                    PhotoError::FileSystemError("找不到有效的Python模块路径".to_string())
                })?;

                let sys_path = sys.getattr("path")?;
                sys_path.call_method1("insert", (0, &python_path_str))?;
                log::info!("🐍 已添加Python路径: {}", python_path_str);

                // 动态获取当前Poetry环境的site-packages路径
                if let Ok(output) = std::process::Command::new("poetry")
                    .args(&["env", "info", "--path"])
                    .current_dir("../src-python")
                    .output()
                {
                    if output.status.success() {
                        let env_path = String::from_utf8_lossy(&output.stdout).trim().to_string();
                        log::info!("🔍 Poetry环境路径: {}", env_path);

                        // 尝试常见的Python版本路径
                        let python_versions = ["3.12", "3.13", "3.11", "3.10"];
                        for version in &python_versions {
                            let site_packages_path =
                                format!("{}/lib/python{}/site-packages", env_path, version);
                            if std::path::Path::new(&site_packages_path).exists() {
                                sys_path.call_method1("insert", (0, &site_packages_path))?;
                                log::info!(
                                    "🐍 已添加Poetry site-packages路径: {}",
                                    site_packages_path
                                );
                                break;
                            }
                        }
                    }
                }

                log::debug!("📦 导入 photo_dedup.collector 模块");
                let photo_module = PyModule::import(py, "photo_dedup.collector").map_err(|e| {
                    log::error!("❌ 导入 photo_dedup.collector 模块失败: {}", e);
                    e
                })?;

                log::debug!("🔍 获取 get_library_info 函数");
                let get_info = photo_module.getattr("get_library_info").map_err(|e| {
                    log::error!("❌ 获取 get_library_info 函数失败: {}", e);
                    e
                })?;

                log::debug!("🚀 调用 get_library_info 函数，参数: {}", path);
                let info_dict = get_info.call1((path,)).map_err(|e| {
                    log::error!("❌ 调用 get_library_info 函数失败: {}", e);
                    e
                })?;

                log::debug!("📊 解析 Python 返回的结果");
                // Convert Python dict to Rust struct
                let info: serde_json::Value = serde_json::from_str(&info_dict.to_string())
                    .map_err(|e| {
                        log::error!("❌ 解析 Python 返回结果失败: {}", e);
                        PhotoError::PythonError(e.to_string())
                    })?;

                Ok(LibraryInfo {
                    path: info["path"].as_str().unwrap_or("").to_string(),
                    name: info["name"].as_str().unwrap_or("").to_string(),
                    photo_count: info["photo_count"].as_u64().unwrap_or(0),
                    last_modified: info["last_modified"].as_i64().unwrap_or(0),
                    r#type: info["type"].as_str().unwrap_or("unknown").to_string(),
                    exists: info["exists"].as_bool().unwrap_or(false),
                    error: info["error"].as_str().map(|s| s.to_string()),
                })
            })
        })
        .await
        .map_err(|e| PhotoError::PythonError(e.to_string()))?;

        result
    }

    /// Collect photos from a library or directory
    pub async fn collect_photos(
        &self,
        library_path: &str,
        _output_dir: &str, // 添加下划线前缀表示故意未使用
        days_back: i32,
        max_photos: i32,
        db_path: Option<String>,
    ) -> Result<Vec<PhotoInfo>, PhotoError> {
        let library_path = library_path.to_string();
        let db_path_clone = db_path.clone();

        let photos = tokio::task::spawn_blocking(move || {
            Python::with_gil(|py| {
                let sys = py.import("sys")?;

                // 获取当前工作目录并记录日志
                let current_dir = std::env::current_dir()
                    .map_err(|e| PhotoError::FileSystemError(format!("无法获取当前目录: {}", e)))?;
                log::info!("🔍 collect_photos - 当前工作目录: {:?}", current_dir);

                // 尝试多个可能的Python路径
                let possible_paths = vec![
                    current_dir.join("../src-python/python"),
                    current_dir.join("src-python/python"),
                    current_dir.join("photo-thumbnail-manager/src-python/python"),
                    current_dir
                        .parent()
                        .unwrap_or(&current_dir)
                        .join("photo-thumbnail-manager/src-python/python"),
                ];

                let mut python_path_str = None;
                for path in possible_paths {
                    log::info!("🔍 collect_photos - 尝试Python路径: {:?}", path);
                    if path.exists() {
                        match path.canonicalize() {
                            Ok(canonical_path) => {
                                python_path_str =
                                    Some(canonical_path.to_str().unwrap().to_string());
                                log::info!(
                                    "✅ collect_photos - 找到有效的Python路径: {:?}",
                                    canonical_path
                                );
                                break;
                            }
                            Err(e) => {
                                log::warn!(
                                    "⚠️ collect_photos - 路径规范化失败: {:?}, 错误: {}",
                                    path,
                                    e
                                );
                            }
                        }
                    } else {
                        log::warn!("⚠️ collect_photos - 路径不存在: {:?}", path);
                    }
                }

                let python_path_str = python_path_str.ok_or_else(|| {
                    PhotoError::FileSystemError("找不到有效的Python模块路径".to_string())
                })?;

                let sys_path = sys.getattr("path")?;
                sys_path.call_method1("insert", (0, &python_path_str))?;
                log::info!("🐍 collect_photos - 已添加Python路径: {}", python_path_str);

                // 动态获取当前Poetry环境的site-packages路径
                if let Ok(output) = std::process::Command::new("poetry")
                    .args(&["env", "info", "--path"])
                    .current_dir("../src-python")
                    .output()
                {
                    if output.status.success() {
                        let env_path = String::from_utf8_lossy(&output.stdout).trim().to_string();
                        log::info!("🔍 collect_photos - Poetry环境路径: {}", env_path);

                        // 尝试常见的Python版本路径
                        let python_versions = ["3.12", "3.13", "3.11", "3.10"];
                        for version in &python_versions {
                            let site_packages_path =
                                format!("{}/lib/python{}/site-packages", env_path, version);
                            if std::path::Path::new(&site_packages_path).exists() {
                                sys_path.call_method1("insert", (0, &site_packages_path))?;
                                log::info!(
                                    "🐍 collect_photos - 已添加Poetry site-packages路径: {}",
                                    site_packages_path
                                );
                                break;
                            }
                        }
                    }
                }

                let photo_module = PyModule::import(py, "photo_dedup.collector")?;

                // Use the new JSON collection function
                let collection_func = photo_module.getattr("collect_photos_json")?;

                // Prepare keyword arguments
                let kwargs = PyDict::new(py);
                if library_path.ends_with(".photoslibrary") {
                    kwargs.set_item("library_path", &library_path)?;
                    kwargs.set_item("days_back", days_back)?;
                } else {
                    kwargs.set_item("directory_path", &library_path)?;
                }
                kwargs.set_item("max_photos", max_photos)?;

                // Add database path if provided
                if let Some(db_path) = db_path_clone {
                    kwargs.set_item("db_path", &db_path)?;
                }

                let photo_list_json = collection_func.call((), Some(&kwargs))?;
                let json_str: String = photo_list_json.extract()?;
                log::info!("📸 收到Python返回的JSON长度: {} 字符", json_str.len());

                // Convert JSON string to Rust Vec<PhotoInfo>
                let photos: Vec<PhotoInfo> = serde_json::from_str(&json_str).map_err(|e| {
                    log::error!("❌ JSON解析失败: {}", e);
                    log::error!(
                        "📄 JSON内容前100字符: {}",
                        &json_str[..json_str.len().min(100)]
                    );
                    PhotoError::PythonError(format!("JSON解析错误: {}", e))
                })?;
                Ok(photos)
            })
        })
        .await
        .map_err(|e| PhotoError::PythonError(e.to_string()))?;

        photos
    }

    /// Collect photos with smart cache by date range
    pub async fn collect_photos_with_smart_cache_by_date_range(
        &self,
        library_path: String,
        start_date: i64,
        end_date: i64,
        max_photos: i32,
        db_path: Option<String>,
    ) -> Result<Vec<PhotoInfo>, PhotoError> {
        log::info!("🧠 collect_photos_with_smart_cache_by_date_range - 开始智能缓存收集");
        log::info!("📚 库路径: {}", library_path);
        log::info!("📅 开始时间戳: {}", start_date);
        log::info!("📅 结束时间戳: {}", end_date);
        log::info!("📊 最大照片数: {}", max_photos);

        let library_path_clone = library_path.clone();
        let db_path_clone = db_path.clone();

        let _guard = self._gil.lock().await;

        let photos = tokio::task::spawn_blocking(move || -> Result<Vec<PhotoInfo>, PhotoError> {
            Python::with_gil(|py| {
                // 设置Python路径
                Self::setup_python_paths_for_task(py)?;

                // 导入collector模块
                let collector_module = py.import("photo_dedup.collector").map_err(|e| {
                    PhotoError::PythonError(format!("Failed to import collector module: {}", e))
                })?;

                // 获取智能缓存函数
                let collect_func = collector_module
                    .getattr("collect_photos_with_smart_cache_by_date_range")
                    .map_err(|e| {
                        PhotoError::PythonError(format!(
                            "Failed to get collect_photos_with_smart_cache_by_date_range function: {}",
                            e
                        ))
                    })?;

                // 调用Python函数
                let result = if let Some(db_path) = db_path_clone {
                    collect_func.call1((
                        library_path_clone,
                        start_date,
                        end_date,
                        max_photos,
                        db_path,
                    ))
                } else {
                    collect_func.call1((
                        library_path_clone,
                        start_date,
                        end_date,
                        max_photos,
                        py.None(),
                    ))
                }
                .map_err(|e| {
                    PhotoError::PythonError(format!("Failed to call collect_photos_with_smart_cache_by_date_range: {}", e))
                })?;

                // 转换结果为JSON字符串
                let json_module = py.import("json").map_err(|e| {
                    PhotoError::PythonError(format!("Failed to import json module: {}", e))
                })?;

                let photos_json = json_module
                    .getattr("dumps")?
                    .call1((result,))?
                    .extract::<String>()
                    .map_err(|e| {
                        PhotoError::PythonError(format!("Failed to extract JSON string: {}", e))
                    })?;

                log::info!("📸 收到智能缓存JSON长度: {} 字符", photos_json.len());

                let photos: Vec<PhotoInfo> = serde_json::from_str(&photos_json).map_err(|e| {
                    PhotoError::PythonError(format!("Failed to parse photos JSON: {}", e))
                })?;

                log::info!("✅ 智能缓存收集完成，返回 {} 张照片", photos.len());
                Ok(photos)
            })
        })
        .await
        .map_err(|e| PhotoError::PythonError(format!("Task join error: {}", e)))??;

        Ok(photos)
    }

    /// Collect photos with smart cache
    pub async fn collect_photos_with_smart_cache(
        &self,
        library_path: String,
        days_to_fetch: i32,
        max_photos: i32,
        db_path: Option<String>,
    ) -> Result<Vec<PhotoInfo>, PhotoError> {
        log::info!("🧠 collect_photos_with_smart_cache - 开始智能缓存收集");
        log::info!("📚 库路径: {}", library_path);
        log::info!("📅 获取天数: {}", days_to_fetch);
        log::info!("📊 最大照片数: {}", max_photos);

        let library_path_clone = library_path.clone();
        let db_path_clone = db_path.clone();

        let photos = tokio::task::spawn_blocking(move || {
            Python::with_gil(|py| -> Result<Vec<PhotoInfo>, PhotoError> {
                // 设置Python路径
                Self::setup_python_paths_for_task(py)?;

                let collector_module = py.import("photo_dedup.collector").map_err(|e| {
                    PhotoError::PythonError(format!("Failed to import collector module: {}", e))
                })?;

                let collect_func = collector_module
                    .getattr("collect_photos_with_smart_cache")
                    .map_err(|e| {
                        PhotoError::PythonError(format!(
                            "Failed to get collect_photos_with_smart_cache function: {}",
                            e
                        ))
                    })?;

                log::info!("🔍 调用Python函数参数:");
                log::info!("  library_path: {}", library_path_clone);
                log::info!("  days_to_fetch: {}", days_to_fetch);
                log::info!("  max_photos: {}", max_photos);
                log::info!("  db_path: {:?}", db_path_clone);

                let result = collect_func
                    .call1((library_path_clone, days_to_fetch, max_photos, db_path_clone))
                    .map_err(|e| {
                        PhotoError::PythonError(format!("Python function call failed: {}", e))
                    })?;

                let photos_json = result.extract::<String>().map_err(|e| {
                    PhotoError::PythonError(format!("Failed to extract result as string: {}", e))
                })?;

                log::info!("📸 收到智能缓存JSON长度: {} 字符", photos_json.len());

                let photos: Vec<PhotoInfo> = serde_json::from_str(&photos_json).map_err(|e| {
                    PhotoError::PythonError(format!("Failed to parse photos JSON: {}", e))
                })?;

                log::info!("✅ 智能缓存收集完成，返回 {} 张照片", photos.len());
                Ok(photos)
            })
        })
        .await
        .map_err(|e| PhotoError::PythonError(format!("Task join error: {}", e)))??;

        Ok(photos)
    }

    /// 清空数据库缓存
    pub async fn clear_database_cache(
        &self,
        db_path: Option<String>,
    ) -> Result<String, PhotoError> {
        let _guard = self._gil.lock().await;

        let task = tokio::task::spawn_blocking(move || -> Result<String, PhotoError> {
            Python::with_gil(|py| {
                // 设置Python路径
                Self::setup_python_paths_for_task(py)?;

                // 导入数据库模块
                let database_module = py.import("photo_dedup.database").map_err(|e| {
                    PhotoError::PythonError(format!("Failed to import database module: {}", e))
                })?;

                // 获取清空数据库函数
                let clear_func = database_module
                    .getattr("clear_database_cache")
                    .map_err(|e| {
                        PhotoError::PythonError(format!(
                            "Failed to get clear_database_cache function: {}",
                            e
                        ))
                    })?;

                // 调用清空数据库函数
                let result = if let Some(db_path) = db_path {
                    clear_func.call1((db_path,))
                } else {
                    clear_func.call0()
                }
                .map_err(|e| {
                    PhotoError::PythonError(format!("Failed to clear database cache: {}", e))
                })?;

                // 转换结果为字符串
                let result_str: String = result.extract().map_err(|e| {
                    PhotoError::PythonError(format!("Failed to extract result: {}", e))
                })?;

                Ok(result_str)
            })
        });

        let result = task
            .await
            .map_err(|e| PhotoError::PythonError(format!("Task join error: {}", e)))??;

        Ok(result)
    }

    /// Collect photos by date range from Photos Library
    pub async fn collect_photos_by_date_range(
        &self,
        library_path: String,
        start_date: i64,
        end_date: i64,
        max_photos: i32,
        db_path: Option<String>,
    ) -> Result<Vec<PhotoInfo>, PhotoError> {
        log::info!("🔍 collect_photos_by_date_range - 开始按日期范围收集照片");
        log::info!("📅 日期范围: {} 到 {}", start_date, end_date);

        let library_path_clone = library_path.clone();
        let db_path_clone = db_path.clone();

        let photos = tokio::task::spawn_blocking(move || {
            Python::with_gil(|py| -> Result<Vec<PhotoInfo>, PhotoError> {
                // Add Python path
                let sys = py.import("sys")?;
                let current_dir = std::env::current_dir()
                    .map_err(|e| PhotoError::FileSystemError(e.to_string()))?;
                log::info!("🔍 collect_photos_by_date_range - 当前工作目录: {:?}", current_dir);

                let python_path = current_dir.join("../src-python/python");
                let absolute_python_path = python_path
                    .canonicalize()
                    .map_err(|e| PhotoError::FileSystemError(e.to_string()))?;
                log::info!("✅ collect_photos_by_date_range - 找到有效的Python路径: {:?}", absolute_python_path);

                let python_path_str = absolute_python_path.to_str().unwrap();
                let sys_path = sys.getattr("path")?;
                sys_path.call_method1("insert", (0, python_path_str))?;
                log::info!("🐍 collect_photos_by_date_range - 已添加Python路径: {}", python_path_str);

                // 动态获取当前Poetry环境的site-packages路径
                if let Ok(output) = std::process::Command::new("poetry")
                    .args(["env", "info", "--path"])
                    .current_dir("../src-python")
                    .output()
                {
                    if output.status.success() {
                        let env_path = String::from_utf8_lossy(&output.stdout).trim().to_string();
                        log::info!("🔍 collect_photos_by_date_range - Poetry环境路径: {env_path}");

                        // 尝试常见的Python版本路径
                        let python_versions = ["3.12", "3.13", "3.11", "3.10"];
                        for version in &python_versions {
                            let site_packages_path =
                                format!("{env_path}/lib/python{version}/site-packages");
                            if std::path::Path::new(&site_packages_path).exists() {
                                sys_path.call_method1("insert", (0, &site_packages_path))?;
                                log::info!(
                                    "🐍 collect_photos_by_date_range - 已添加Poetry site-packages路径: {site_packages_path}"
                                );
                                break;
                            }
                        }
                    }
                }

                // Import the photo collection module
                let photo_module = PyModule::import(py, "photo_dedup.collector")?;
                let collection_func = photo_module.getattr("collect_photos_by_date_range_json")?;

                // Prepare keyword arguments
                let kwargs = PyDict::new(py);
                kwargs.set_item("library_path", &library_path_clone)?;
                kwargs.set_item("start_date", start_date)?;
                kwargs.set_item("end_date", end_date)?;
                kwargs.set_item("max_photos", max_photos)?;

                // Add database path if provided
                if let Some(db_path) = db_path_clone {
                    kwargs.set_item("db_path", &db_path)?;
                }

                // 记录调用参数
                log::info!("🔍 调用Python函数参数:");
                log::info!("  library_path: {}", library_path_clone);
                log::info!("  start_date: {} ({})", start_date,
                    chrono::DateTime::from_timestamp(start_date, 0)
                        .map(|dt| dt.format("%Y-%m-%d %H:%M:%S").to_string())
                        .unwrap_or_else(|| "无效时间戳".to_string()));
                log::info!("  end_date: {} ({})", end_date,
                    chrono::DateTime::from_timestamp(end_date, 0)
                        .map(|dt| dt.format("%Y-%m-%d %H:%M:%S").to_string())
                        .unwrap_or_else(|| "无效时间戳".to_string()));
                log::info!("  max_photos: {}", max_photos);

                let photo_list_json = collection_func.call((), Some(&kwargs))?;
                let json_str: String = photo_list_json.extract()?;
                log::info!("📸 收到按日期范围的JSON长度: {} 字符", json_str.len());

                // 如果JSON很短，可能是空结果，记录内容
                if json_str.len() < 100 {
                    log::info!("📸 JSON内容: {}", json_str);
                }

                // Convert JSON string to Rust Vec<PhotoInfo>
                let photos: Vec<PhotoInfo> = serde_json::from_str(&json_str).map_err(|e| {
                    log::error!("❌ 按日期范围JSON解析失败: {}", e);
                    PhotoError::PythonError(format!("JSON解析错误: {}", e))
                })?;
                Ok(photos)
            })
        })
        .await
        .map_err(|e| PhotoError::PythonError(e.to_string()))?;

        photos
    }

    /// Collect photos by date added range from Photos Library
    pub async fn collect_photos_by_date_added_range(
        &self,
        library_path: String,
        start_date: i64,
        end_date: i64,
        max_photos: i32,
        db_path: Option<String>,
    ) -> Result<Vec<PhotoInfo>, PhotoError> {
        log::info!("🔍 collect_photos_by_date_added_range - 开始按添加日期范围收集照片");
        log::info!("📅 添加日期范围: {} 到 {}", start_date, end_date);

        let library_path_clone = library_path.clone();
        let db_path_clone = db_path.clone();

        let photos = tokio::task::spawn_blocking(move || {
            Python::with_gil(|py| -> Result<Vec<PhotoInfo>, PhotoError> {
                // Add Python path
                let sys = py.import("sys")?;
                let current_dir = std::env::current_dir()
                    .map_err(|e| PhotoError::FileSystemError(e.to_string()))?;

                let python_path = current_dir.join("../src-python/python");
                let absolute_python_path = python_path
                    .canonicalize()
                    .map_err(|e| PhotoError::FileSystemError(e.to_string()))?;

                let python_path_str = absolute_python_path.to_str().unwrap();
                let sys_path = sys.getattr("path")?;
                sys_path.call_method1("insert", (0, python_path_str))?;

                // 动态获取当前Poetry环境的site-packages路径
                if let Ok(output) = std::process::Command::new("poetry")
                    .args(["env", "info", "--path"])
                    .current_dir("../src-python")
                    .output()
                {
                    if output.status.success() {
                        let env_path = String::from_utf8_lossy(&output.stdout).trim().to_string();
                        let python_versions = ["3.12", "3.13", "3.11", "3.10"];
                        for version in &python_versions {
                            let site_packages_path =
                                format!("{env_path}/lib/python{version}/site-packages");
                            if std::path::Path::new(&site_packages_path).exists() {
                                sys_path.call_method1("insert", (0, &site_packages_path))?;
                                break;
                            }
                        }
                    }
                }

                // Import the photo collection module
                let photo_module = PyModule::import(py, "photo_dedup.collector")?;
                let collection_func = photo_module.getattr("collect_photos_by_date_added_range")?;

                // Create kwargs dictionary
                let kwargs = PyDict::new(py);
                kwargs.set_item("library_path", library_path_clone)?;
                kwargs.set_item("start_date", start_date)?;
                kwargs.set_item("end_date", end_date)?;
                kwargs.set_item("max_photos", max_photos)?;
                if let Some(db_path) = db_path_clone {
                    kwargs.set_item("db_path", db_path)?;
                }

                let photo_list = collection_func.call((), Some(&kwargs))?;

                // Convert Python list to Rust Vec<PhotoInfo>
                let photos: Vec<PhotoInfo> = photo_list.extract()?;
                Ok(photos)
            })
        })
        .await
        .map_err(|e| PhotoError::PythonError(e.to_string()))?;

        photos
    }

    /// Collect photos timeline by added date from Photos Library
    pub async fn collect_photos_timeline_by_added_date(
        &self,
        library_path: String,
        days_per_segment: i32,
        max_segments: i32,
        max_photos_per_segment: i32,
    ) -> Result<Vec<PhotoInfo>, PhotoError> {
        log::info!("🔍 collect_photos_timeline_by_added_date - 开始按添加时间线收集照片");
        log::info!(
            "📅 参数: 每段{}天, 最大{}段, 每段最大{}张照片",
            days_per_segment,
            max_segments,
            max_photos_per_segment
        );

        let library_path_clone = library_path.clone();

        let photos = tokio::task::spawn_blocking(move || {
            Python::with_gil(|py| -> Result<Vec<PhotoInfo>, PhotoError> {
                // Add Python path
                let sys = py.import("sys")?;
                let current_dir = std::env::current_dir()
                    .map_err(|e| PhotoError::FileSystemError(e.to_string()))?;

                let python_path = current_dir.join("../src-python/python");
                let absolute_python_path = python_path
                    .canonicalize()
                    .map_err(|e| PhotoError::FileSystemError(e.to_string()))?;

                let python_path_str = absolute_python_path.to_str().unwrap();
                let sys_path = sys.getattr("path")?;
                sys_path.call_method1("insert", (0, python_path_str))?;

                // 动态获取当前Poetry环境的site-packages路径
                if let Ok(output) = std::process::Command::new("poetry")
                    .args(["env", "info", "--path"])
                    .current_dir("../src-python")
                    .output()
                {
                    if output.status.success() {
                        let env_path = String::from_utf8_lossy(&output.stdout).trim().to_string();
                        let python_versions = ["3.12", "3.13", "3.11", "3.10"];
                        for version in &python_versions {
                            let site_packages_path =
                                format!("{env_path}/lib/python{version}/site-packages");
                            if std::path::Path::new(&site_packages_path).exists() {
                                sys_path.call_method1("insert", (0, &site_packages_path))?;
                                break;
                            }
                        }
                    }
                }

                // Import the photo collection module
                let photo_module = PyModule::import(py, "photo_dedup.collector")?;
                let collection_func =
                    photo_module.getattr("collect_photos_timeline_by_added_date")?;

                // Create kwargs dictionary
                let kwargs = PyDict::new(py);
                kwargs.set_item("library_path", library_path_clone)?;
                kwargs.set_item("days_per_segment", days_per_segment)?;
                kwargs.set_item("max_segments", max_segments)?;
                kwargs.set_item("max_photos_per_segment", max_photos_per_segment)?;

                let photo_list = collection_func.call((), Some(&kwargs))?;

                // Convert Python list to Rust Vec<PhotoInfo>
                let photos: Vec<PhotoInfo> = photo_list.extract()?;
                Ok(photos)
            })
        })
        .await
        .map_err(|e| PhotoError::PythonError(e.to_string()))?;

        photos
    }

    /// Analyze photos using timeline-based grouping with async hash calculation
    pub async fn analyze_photos_by_timeline(
        &self,
        photos: Vec<PhotoInfo>,
        time_threshold_seconds: i32,
        max_workers: i32,
        ahash_threshold: f32,
        phash_threshold: f32,
    ) -> Result<String, PhotoError> {
        log::info!("⏰ analyze_photos_by_timeline - 开始时间线分析");
        log::info!(
            "📊 配置: time_threshold_seconds={}, max_workers={}, ahash_threshold={}, phash_threshold={}",
            time_threshold_seconds,
            max_workers,
            ahash_threshold,
            phash_threshold
        );

        let photos_clone = photos.clone();

        let result = tokio::task::spawn_blocking(move || {
            Python::with_gil(|py| -> Result<String, PhotoError> {
                // Add Python path
                let sys = py.import("sys")?;
                let current_dir = std::env::current_dir()
                    .map_err(|e| PhotoError::FileSystemError(e.to_string()))?;

                let python_path = current_dir.join("../src-python/python");
                let absolute_python_path = python_path
                    .canonicalize()
                    .map_err(|e| PhotoError::FileSystemError(e.to_string()))?;

                let python_path_str = absolute_python_path.to_str().unwrap();
                let sys_path = sys.getattr("path")?;
                sys_path.call_method1("insert", (0, python_path_str))?;

                // Import the timeline analyzer module
                let timeline_module = PyModule::import(py, "photo_dedup.timeline_analyzer")?;
                let analyze_func = timeline_module.getattr("analyze_photos_by_timeline")?;

                // Convert photos to JSON
                let photos_json = serde_json::to_string(&photos_clone)
                    .map_err(|e| PhotoError::PythonError(format!("序列化照片数据失败: {}", e)))?;

                // Call the function with JSON string and thresholds
                let result = analyze_func.call1((
                    photos_json,
                    time_threshold_seconds,
                    max_workers,
                    ahash_threshold,
                    phash_threshold,
                ))?;

                let result_str: String = result.extract()?;
                Ok(result_str)
            })
        })
        .await
        .map_err(|e| PhotoError::PythonError(format!("Task join error: {}", e)))?
        .map_err(|e| PhotoError::PythonError(format!("Python execution error: {}", e)))?;

        log::info!("✅ analyze_photos_by_timeline - 时间线分析完成");
        Ok(result)
    }

    /// Add photos to existing timeline analyzer incrementally
    pub async fn add_photos_to_timeline_incrementally(
        &self,
        analyzer_id: String,
        new_photos: Vec<PhotoInfo>,
    ) -> Result<String, PhotoError> {
        log::info!("🔄 add_photos_to_timeline_incrementally - 开始增量分析");
        log::info!(
            "📊 配置: analyzer_id={}, new_photos={}",
            analyzer_id,
            new_photos.len()
        );

        let photos_clone = new_photos.clone();

        let result = tokio::task::spawn_blocking(move || {
            Python::with_gil(|py| -> Result<String, PhotoError> {
                // Add Python path
                let sys = py.import("sys")?;
                let current_dir = std::env::current_dir()
                    .map_err(|e| PhotoError::FileSystemError(e.to_string()))?;

                let python_path = current_dir.join("../src-python/python");
                let absolute_python_path = python_path
                    .canonicalize()
                    .map_err(|e| PhotoError::FileSystemError(e.to_string()))?;

                let python_path_str = absolute_python_path.to_str().unwrap();
                let sys_path = sys.getattr("path")?;
                sys_path.call_method1("insert", (0, python_path_str))?;

                // Import the timeline analyzer module
                let _timeline_module = PyModule::import(py, "photo_dedup.timeline_analyzer")?;
                let _add_func = _timeline_module.getattr("add_photos_to_timeline_incrementally")?;

                // Convert photos to JSON
                let _photos_json = serde_json::to_string(&photos_clone)
                    .map_err(|e| PhotoError::PythonError(format!("序列化照片数据失败: {}", e)))?;

                // Note: For now, we'll use a simple approach where analyzer_id is ignored
                // In a full implementation, you'd want to maintain analyzer instances
                log::warn!("⚠️ 增量分析功能需要维护分析器实例状态，当前为简化实现");

                // For now, return a placeholder result
                let placeholder_result = serde_json::json!({
                    "similarity_groups": [],
                    "timeline_groups_count": 0,
                    "affected_groups_count": 0,
                    "new_photos_count": photos_clone.len(),
                    "total_photos": photos_clone.len(),
                    "total_time_seconds": 0.0,
                    "performance_stats": {
                        "timeline_groups": 0,
                        "affected_groups": 0,
                        "avg_photos_per_group": 0.0,
                        "time_threshold_seconds": 300
                    },
                    "note": "增量分析功能正在开发中，当前返回占位符结果"
                });

                Ok(placeholder_result.to_string())
            })
        })
        .await
        .map_err(|e| PhotoError::PythonError(format!("Task join error: {}", e)))?
        .map_err(|e| PhotoError::PythonError(format!("Python execution error: {}", e)))?;

        log::info!("✅ add_photos_to_timeline_incrementally - 增量分析完成");
        Ok(result)
    }

    /// Analyze photos using optimized algorithm with layered detection
    pub async fn analyze_photos_optimized(
        &self,
        photos: Vec<PhotoInfo>,
        db_path: Option<String>,
        batch_size: i32,
        max_workers: i32,
        ahash_threshold: f32,
        phash_threshold: f32,
    ) -> Result<String, PhotoError> {
        log::info!("🚀 analyze_photos_optimized - 开始优化分析");
        log::info!(
            "📊 配置: batch_size={}, max_workers={}, ahash_threshold={}, phash_threshold={}",
            batch_size,
            max_workers,
            ahash_threshold,
            phash_threshold
        );

        let photos_clone = photos.clone();
        let db_path_clone = db_path.clone();

        let result = tokio::task::spawn_blocking(move || {
            Python::with_gil(|py| -> Result<String, PhotoError> {
                // Add Python path
                let sys = py.import("sys")?;
                let current_dir = std::env::current_dir()
                    .map_err(|e| PhotoError::FileSystemError(e.to_string()))?;

                let python_path = current_dir.join("../src-python/python");
                let absolute_python_path = python_path
                    .canonicalize()
                    .map_err(|e| PhotoError::FileSystemError(e.to_string()))?;

                let python_path_str = absolute_python_path.to_str().unwrap();
                let sys_path = sys.getattr("path")?;
                sys_path.call_method1("insert", (0, python_path_str))?;

                // Import the optimized analyzer module
                let analyzer_module = PyModule::import(py, "photo_dedup.optimized_analyzer")?;
                let analyze_func = analyzer_module.getattr("analyze_photos_optimized")?;

                // Convert photos to JSON
                let photos_json = serde_json::to_string(&photos_clone)
                    .map_err(|e| PhotoError::PythonError(format!("序列化照片数据失败: {}", e)))?;

                // Prepare keyword arguments
                let kwargs = PyDict::new(py);

                // Create config dict
                let config_dict = PyDict::new(py);
                config_dict.set_item("batch_size", batch_size)?;
                config_dict.set_item("max_workers", max_workers)?;
                config_dict.set_item("ahash_threshold", ahash_threshold)?;
                config_dict.set_item("phash_threshold", phash_threshold)?;
                config_dict.set_item("enable_cache", true)?;

                if let Some(db_path) = db_path_clone {
                    config_dict.set_item("cache_db_path", &db_path)?;
                } else {
                    // 使用临时目录避免触发Tauri开发模式重载
                    let temp_dir = std::env::temp_dir();
                    let cache_db_path = temp_dir.join("photo_manager.db");
                    config_dict.set_item("cache_db_path", cache_db_path.to_str().unwrap())?;
                    log::info!("🗄️ 使用临时目录数据库: {:?}", cache_db_path);
                }

                kwargs.set_item("photos_json", &photos_json)?;
                kwargs.set_item("config_dict", config_dict)?;

                let result_json = analyze_func.call((), Some(&kwargs))?;
                let json_str: String = result_json.extract()?;

                log::info!("📈 优化分析结果JSON长度: {} 字符", json_str.len());
                Ok(json_str)
            })
        })
        .await
        .map_err(|e| PhotoError::PythonError(e.to_string()))?;

        result
    }

    /// Generate thumbnails for a list of photos
    pub async fn generate_thumbnails(
        &self,
        photos: Vec<PhotoInfo>,
        config: &crate::models::ThumbnailRequest,
        db_path: Option<String>,
    ) -> Result<Vec<PhotoInfo>, PhotoError> {
        let photos_json =
            serde_json::to_string(&photos).map_err(|e| PhotoError::PythonError(e.to_string()))?;
        let _output_dir = config.output_dir.clone();
        let _thumbnail_size = config.thumbnail_size.clone();
        let _quality = config.quality;
        let db_path_clone = db_path.clone();

        let updated_photos = tokio::task::spawn_blocking(move || {
            Python::with_gil(|py| {
                let sys = py.import("sys")?;
                let absolute_python_path = std::env::current_dir()
                    .unwrap()
                    .join("src-python/python")
                    .canonicalize()
                    .map_err(|e| PhotoError::FileSystemError(e.to_string()))?;
                let python_path_str = absolute_python_path.to_str().unwrap();
                let sys_path = sys.getattr("path")?;
                sys_path.call_method1("insert", (0, python_path_str))?;

                let thumbnail_module = PyModule::import(py, "photo_dedup.thumbnail")?;
                let generate_thumbnails = thumbnail_module.getattr("generate_thumbnails_batch")?;

                // Prepare keyword arguments
                let kwargs = PyDict::new(py);
                kwargs.set_item("photos", &photos_json)?;
                kwargs.set_item("config", &PyDict::new(py))?;

                // Add database path if provided
                if let Some(db_path) = db_path_clone {
                    kwargs.set_item("db_path", &db_path)?;
                }

                let result = generate_thumbnails.call((), Some(&kwargs))?;

                // Convert result back to Rust structs
                let updated_photos: Vec<PhotoInfo> = serde_json::from_str(&result.to_string())
                    .map_err(|e| PhotoError::PythonError(e.to_string()))?;

                Ok(updated_photos)
            })
        })
        .await
        .map_err(|e| PhotoError::PythonError(e.to_string()))?;

        updated_photos
    }

    /// Detect temporal groups
    pub async fn detect_temporal_groups(
        &self,
        photos: Vec<PhotoInfo>,
        interval_seconds: i32,
        similarity_threshold: f32,
        db_path: Option<String>,
    ) -> Result<Vec<SimilarityGroup>, PhotoError> {
        let photos_json =
            serde_json::to_string(&photos).map_err(|e| PhotoError::PythonError(e.to_string()))?;
        let db_path_clone = db_path.clone();

        let groups = tokio::task::spawn_blocking(move || {
            Python::with_gil(|py| {
                let sys = py.import("sys")?;

                // 获取当前工作目录并记录日志
                let current_dir = std::env::current_dir()
                    .map_err(|e| PhotoError::FileSystemError(format!("无法获取当前目录: {}", e)))?;
                log::info!(
                    "🔍 detect_temporal_groups - 当前工作目录: {:?}",
                    current_dir
                );

                // 尝试多个可能的Python路径
                let possible_paths = vec![
                    current_dir.join("../src-python/python"),
                    current_dir.join("src-python/python"),
                    current_dir.join("photo-thumbnail-manager/src-python/python"),
                    current_dir
                        .parent()
                        .unwrap_or(&current_dir)
                        .join("photo-thumbnail-manager/src-python/python"),
                ];

                let mut python_path_str = None;
                for path in possible_paths {
                    log::info!("🔍 detect_temporal_groups - 尝试Python路径: {:?}", path);
                    if path.exists() {
                        match path.canonicalize() {
                            Ok(canonical_path) => {
                                python_path_str =
                                    Some(canonical_path.to_str().unwrap().to_string());
                                log::info!(
                                    "✅ detect_temporal_groups - 找到有效的Python路径: {:?}",
                                    canonical_path
                                );
                                break;
                            }
                            Err(e) => {
                                log::warn!(
                                    "⚠️ detect_temporal_groups - 路径规范化失败: {:?}, 错误: {}",
                                    path,
                                    e
                                );
                            }
                        }
                    } else {
                        log::warn!("⚠️ detect_temporal_groups - 路径不存在: {:?}", path);
                    }
                }

                let python_path_str = python_path_str.ok_or_else(|| {
                    PhotoError::FileSystemError("找不到有效的Python模块路径".to_string())
                })?;

                let sys_path = sys.getattr("path")?;
                sys_path.call_method1("insert", (0, &python_path_str))?;
                log::info!(
                    "🐍 detect_temporal_groups - 已添加Python路径: {}",
                    python_path_str
                );

                let analyzer_module = PyModule::import(py, "photo_dedup.analyzer")?;
                let detect_groups = analyzer_module.getattr("detect_temporal_groups_json")?;

                // Prepare keyword arguments
                let kwargs = PyDict::new(py);
                kwargs.set_item("photos_json", &photos_json)?;
                kwargs.set_item("time_threshold_seconds", interval_seconds)?;
                kwargs.set_item("similarity_threshold", similarity_threshold)?;

                // Add database path if provided
                if let Some(db_path) = db_path_clone {
                    kwargs.set_item("db_path", &db_path)?;
                }

                let result = detect_groups.call((), Some(&kwargs))?;
                let json_str: String = result.extract()?;
                log::info!("🔍 收到分析结果JSON长度: {} 字符", json_str.len());

                // Convert result back to Rust structs
                let groups: Vec<SimilarityGroup> =
                    serde_json::from_str(&json_str).map_err(|e| {
                        log::error!("❌ 分析结果JSON解析失败: {}", e);
                        log::error!(
                            "📄 JSON内容前100字符: {}",
                            &json_str[..json_str.len().min(100)]
                        );
                        PhotoError::PythonError(format!("分析结果JSON解析错误: {}", e))
                    })?;

                Ok(groups)
            })
        })
        .await
        .map_err(|e| PhotoError::PythonError(e.to_string()))?;

        groups
    }

    /// Get detailed thumbnail information for a specific photo
    pub async fn get_photo_thumbnail_details(
        &self,
        photo_uuid: String,
        library_path: Option<String>,
    ) -> Result<crate::commands::ThumbnailDetails, PhotoError> {
        let uuid_clone = photo_uuid.clone();
        let library_path_clone = library_path.clone();

        let details = tokio::task::spawn_blocking(move || {
            Python::with_gil(|py| {
                let sys = py.import("sys")?;

                // Add Python path
                let current_dir = std::env::current_dir()
                    .map_err(|e| PhotoError::FileSystemError(format!("无法获取当前目录: {}", e)))?;
                let possible_paths = vec![
                    current_dir.join("../src-python/python"),
                    current_dir.join("src-python/python"),
                ];

                let mut python_path_str = None;
                for path in possible_paths {
                    if path.exists() {
                        python_path_str = Some(path.to_str().unwrap().to_string());
                        break;
                    }
                }

                let python_path = python_path_str.ok_or_else(|| {
                    PhotoError::FileSystemError("找不到有效的Python路径".to_string())
                })?;

                let sys_path = sys.getattr("path")?;
                sys_path.call_method1("insert", (0, &python_path))?;

                // Import thumbnail module
                let thumbnail_module = PyModule::import(py, "photo_dedup.thumbnail")?;
                let get_details_func = thumbnail_module.getattr("get_photo_thumbnail_details")?;

                // Call Python function with optional library_path
                let result = if let Some(lib_path) = library_path_clone {
                    get_details_func.call1((uuid_clone, lib_path))?
                } else {
                    get_details_func.call1((uuid_clone,))?
                };
                let json_str: String = result.extract()?;

                // Parse result
                let details: crate::commands::ThumbnailDetails = serde_json::from_str(&json_str)
                    .map_err(|e| PhotoError::PythonError(format!("JSON解析错误: {}", e)))?;

                Ok(details)
            })
        })
        .await
        .map_err(|e| PhotoError::PythonError(e.to_string()))?;

        details
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_bridge_initialization() {
        let bridge = PythonBridge::new().unwrap();
        assert!(bridge._gil.try_lock().is_ok());
    }

    #[tokio::test]
    async fn test_get_library_info_invalid() {
        let bridge = PythonBridge::new().unwrap();
        let result = bridge.get_library_info("/invalid/path").await;
        assert!(result.is_err());
    }
}
